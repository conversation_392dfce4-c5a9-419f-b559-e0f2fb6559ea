#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FFmpeg Audio Extractor GUI Tool
メインアプリケーションファイル
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
import queue
import json
import subprocess
import concurrent.futures
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import multiprocessing

class FFmpegAudioExtractor:
    """FFmpegオーディオ抽出GUIアプリケーション"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FFmpeg Audio Extractor")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 設定ファイルパス
        self.config_file = "config.json"
        
        # アプリケーション状態
        self.file_list = []  # 処理対象ファイルリスト
        self.processing = False  # 処理中フラグ
        self.executor = None  # 並列処理用エグゼキューター
        self.progress_queue = queue.Queue()  # 進捗通知用キュー
        
        # デフォルト設定
        self.settings = {
            "ffmpeg_path": "",
            "ffprobe_path": "",  # FFprobeパスを追加
            "output_mode": "same_folder",  # same_folder, custom_folder
            "custom_output_path": "",
            "create_subfolder": False,
            "extraction_mode": "copy",  # copy, mp3, aac
            "audio_bitrate": "192",  # kbps
            "parallel_count": min(4, multiprocessing.cpu_count())
        }
        
        # 設定読み込み
        self.load_settings()
        
        # GUI初期化
        self.setup_gui()
        
        # 進捗更新タイマー
        self.root.after(100, self.update_progress)
    
    def setup_gui(self):
        """GUI要素の初期化"""
        # メインフレーム
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # ウィンドウのリサイズ設定
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        
        # 左側パネル（設定エリア）
        self.setup_settings_panel(main_frame)
        
        # 右側パネル（ファイルリストと実行エリア）
        self.setup_main_panel(main_frame)
    
    def setup_settings_panel(self, parent):
        """設定パネルの作成"""
        settings_frame = ttk.LabelFrame(parent, text="設定", padding="10")
        settings_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        settings_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # FFmpegパス設定
        ttk.Label(settings_frame, text="FFmpegパス:").grid(row=row, column=0, sticky=tk.W, pady=2)
        row += 1

        ffmpeg_frame = ttk.Frame(settings_frame)
        ffmpeg_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        ffmpeg_frame.columnconfigure(0, weight=1)

        self.ffmpeg_path_var = tk.StringVar(value=self.settings["ffmpeg_path"])
        self.ffmpeg_entry = ttk.Entry(ffmpeg_frame, textvariable=self.ffmpeg_path_var)
        self.ffmpeg_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(ffmpeg_frame, text="参照...",
                  command=self.browse_ffmpeg).grid(row=0, column=1)
        row += 1

        # FFprobeパス設定
        ttk.Label(settings_frame, text="FFprobeパス:").grid(row=row, column=0, sticky=tk.W, pady=2)
        row += 1

        ffprobe_frame = ttk.Frame(settings_frame)
        ffprobe_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        ffprobe_frame.columnconfigure(0, weight=1)

        self.ffprobe_path_var = tk.StringVar(value=self.settings["ffprobe_path"])
        self.ffprobe_entry = ttk.Entry(ffprobe_frame, textvariable=self.ffprobe_path_var)
        self.ffprobe_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(ffprobe_frame, text="参照...",
                  command=self.browse_ffprobe).grid(row=0, column=1)
        ttk.Button(ffprobe_frame, text="自動検出",
                  command=self.auto_detect_ffprobe).grid(row=0, column=2, padx=(5, 0))
        row += 1
        
        # 抽出オプション
        ttk.Label(settings_frame, text="抽出オプション:").grid(row=row, column=0, sticky=tk.W, pady=(10, 2))
        row += 1
        
        self.extraction_mode_var = tk.StringVar(value=self.settings["extraction_mode"])
        
        extraction_frame = ttk.Frame(settings_frame)
        extraction_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Radiobutton(extraction_frame, text="無劣化コピー", 
                       variable=self.extraction_mode_var, value="copy").pack(anchor=tk.W)
        ttk.Radiobutton(extraction_frame, text="MP3に変換", 
                       variable=self.extraction_mode_var, value="mp3").pack(anchor=tk.W)
        ttk.Radiobutton(extraction_frame, text="AACに変換", 
                       variable=self.extraction_mode_var, value="aac").pack(anchor=tk.W)
        row += 1
        
        # ビットレート設定
        bitrate_frame = ttk.Frame(settings_frame)
        bitrate_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Label(bitrate_frame, text="ビットレート:").pack(side=tk.LEFT)
        self.bitrate_var = tk.StringVar(value=self.settings["audio_bitrate"])
        bitrate_combo = ttk.Combobox(bitrate_frame, textvariable=self.bitrate_var, 
                                   values=["128", "192", "256", "320"], width=10)
        bitrate_combo.pack(side=tk.LEFT, padx=(5, 0))
        row += 1
        
        # 出力先設定
        ttk.Label(settings_frame, text="出力先:").grid(row=row, column=0, sticky=tk.W, pady=(10, 2))
        row += 1
        
        self.output_mode_var = tk.StringVar(value=self.settings["output_mode"])
        
        output_frame = ttk.Frame(settings_frame)
        output_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Radiobutton(output_frame, text="元のファイルと同じフォルダ", 
                       variable=self.output_mode_var, value="same_folder").grid(row=0, column=0, columnspan=2, sticky=tk.W)
        
        ttk.Radiobutton(output_frame, text="指定フォルダ:", 
                       variable=self.output_mode_var, value="custom_folder").grid(row=1, column=0, sticky=tk.W)
        
        custom_path_frame = ttk.Frame(output_frame)
        custom_path_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        custom_path_frame.columnconfigure(0, weight=1)
        
        self.custom_output_var = tk.StringVar(value=self.settings["custom_output_path"])
        self.custom_output_entry = ttk.Entry(custom_path_frame, textvariable=self.custom_output_var)
        self.custom_output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(custom_path_frame, text="参照...", 
                  command=self.browse_output_folder).grid(row=0, column=1)
        
        # サブフォルダ作成オプション
        self.subfolder_var = tk.BooleanVar(value=self.settings["create_subfolder"])
        ttk.Checkbutton(output_frame, text="ファイル名のサブフォルダを作成", 
                       variable=self.subfolder_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=2)
        row += 1
        
        # 並列処理数
        ttk.Label(settings_frame, text="並列処理数:").grid(row=row, column=0, sticky=tk.W, pady=(10, 2))
        row += 1
        
        parallel_frame = ttk.Frame(settings_frame)
        parallel_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        self.parallel_var = tk.IntVar(value=self.settings["parallel_count"])
        parallel_spin = ttk.Spinbox(parallel_frame, from_=1, to=multiprocessing.cpu_count(), 
                                  textvariable=self.parallel_var, width=10)
        parallel_spin.pack(side=tk.LEFT)
        
        ttk.Label(parallel_frame, text=f"(最大: {multiprocessing.cpu_count()})").pack(side=tk.LEFT, padx=(5, 0))
    
    def setup_main_panel(self, parent):
        """メインパネル（ファイルリストと実行エリア）の作成"""
        main_panel = ttk.Frame(parent)
        main_panel.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_panel.columnconfigure(0, weight=1)
        main_panel.rowconfigure(0, weight=1)
        
        # ファイルリストエリア
        self.setup_file_list_area(main_panel)
        
        # 実行エリア
        self.setup_execution_area(main_panel)
    
    def setup_file_list_area(self, parent):
        """ファイルリストエリアの作成"""
        file_frame = ttk.LabelFrame(parent, text="処理対象ファイル", padding="10")
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)
        
        # ファイル追加ボタン
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(button_frame, text="ファイルを追加",
                  command=self.add_files).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="フォルダから追加",
                  command=self.add_files_from_folder).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="選択ファイルを削除",
                  command=self.remove_selected_files).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="すべてクリア",
                  command=self.clear_all_files).pack(side=tk.LEFT)

        # ドラッグ&ドロップの説明ラベル
        info_frame = ttk.Frame(file_frame)
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(35, 0))

        info_label = ttk.Label(info_frame, text="💡 ヒント: 右クリックでメニュー表示、Ctrl+Vでクリップボードから貼り付け",
                              font=("", 8), foreground="gray")
        info_label.pack(side=tk.RIGHT)
        
        # ファイルリスト（Treeview）
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Treeviewとスクロールバー（進捗・時間カラムを追加）
        self.file_tree = ttk.Treeview(
            list_frame,
            columns=("status", "progress", "time", "path"),
            show="headings",
            selectmode="extended"
        )
        self.file_tree.heading("#1", text="ステータス")
        self.file_tree.heading("#2", text="進捗")
        self.file_tree.heading("#3", text="時間")
        self.file_tree.heading("#4", text="ファイルパス")
        self.file_tree.column("#1", width=100, minwidth=80)
        self.file_tree.column("#2", width=150, minwidth=120)
        self.file_tree.column("#3", width=90, minwidth=70)
        self.file_tree.column("#4", width=400, minwidth=200)
        
        scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # ドラッグ&ドロップ設定
        self.setup_drag_drop()
    
    def setup_execution_area(self, parent):
        """実行エリアの作成"""
        exec_frame = ttk.LabelFrame(parent, text="実行・進捗", padding="10")
        exec_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        exec_frame.columnconfigure(0, weight=1)
        exec_frame.rowconfigure(2, weight=1)
        
        # 実行ボタン
        button_frame = ttk.Frame(exec_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_button = ttk.Button(button_frame, text="処理開始", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="処理中止", command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT)
        
        # 進捗バー
        progress_frame = ttk.Frame(exec_frame)
        progress_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(1, weight=1)
        
        ttk.Label(progress_frame, text="進捗:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="0/0")
        self.progress_label.grid(row=0, column=2)
        
        # ログエリア
        log_frame = ttk.Frame(exec_frame)
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
    
    def run(self):
        """アプリケーション実行"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """アプリケーション終了時の処理"""
        if self.processing:
            if messagebox.askokcancel("確認", "処理中です。終了しますか？"):
                self.stop_processing()
                # 少し長めに待機してから強制終了
                self.root.after(3000, self.force_quit)
            return

        self.save_settings()
        self.root.destroy()

    def force_quit(self):
        """強制終了"""
        try:
            # 残っているプロセスを強制終了
            if self.executor:
                self.executor.shutdown(wait=False)

            # すべてのスレッドを強制終了
            import threading
            for thread in threading.enumerate():
                if thread != threading.current_thread() and thread.daemon:
                    try:
                        thread.join(timeout=0.1)
                    except:
                        pass
        except:
            pass
        finally:
            self.root.destroy()

    def setup_drag_drop(self):
        """ドラッグ&ドロップの設定"""
        # 基本的なイベントバインディング
        self.file_tree.bind("<Button-1>", self.on_tree_click)
        self.file_tree.bind("<Double-1>", self.on_tree_double_click)

        # ドラッグ&ドロップの実装を試行
        self.setup_file_drop_area()

        # tkinterdnd2が利用可能な場合のドラッグ&ドロップ設定
        try:
            import tkinterdnd2 as tkdnd
            # tkinterdnd2が利用可能な場合
            self.root = tkdnd.Tk()  # 既存のrootを置き換え
            self.setup_advanced_drag_drop()
        except ImportError:
            # tkinterdnd2が利用できない場合は代替手段を使用
            self.setup_alternative_drag_drop()

    def setup_advanced_drag_drop(self):
        """tkinterdnd2を使用した高度なドラッグ&ドロップ"""
        try:
            import tkinterdnd2 as tkdnd

            # ファイルドロップを受け入れる設定
            self.file_tree.drop_target_register(tkdnd.DND_FILES)
            self.file_tree.dnd_bind('<<Drop>>', self.on_file_drop)

            # ドロップエリアの視覚的フィードバック
            self.file_tree.dnd_bind('<<DragEnter>>', self.on_drag_enter)
            self.file_tree.dnd_bind('<<DragLeave>>', self.on_drag_leave)

        except ImportError:
            pass  # tkinterdnd2が利用できない場合は何もしない

    def setup_alternative_drag_drop(self):
        """代替のドラッグ&ドロップ機能"""
        # Windowsでのドラッグ&ドロップを模擬する機能
        # ファイルエクスプローラーからのコピー&ペースト機能を追加
        self.root.bind('<Control-v>', self.paste_files_from_clipboard)

        # ドロップエリアの説明ラベルを追加
        self.add_drop_instruction()

    def add_drop_instruction(self):
        """ドロップ方法の説明を追加"""
        # ファイルリストの上部に説明を追加
        instruction_text = "ファイル追加方法: 1) 「ファイルを追加」ボタン 2) 右クリックメニュー 3) Ctrl+V（ファイルをコピー後）"
        # この説明は既存のボタンエリアに統合済み

    def on_file_drop(self, event):
        """ファイルドロップイベントの処理（tkinterdnd2使用時）"""
        try:
            files = event.data.split()
            for file_path in files:
                # ファイルパスのクリーンアップ
                file_path = file_path.strip('{}')  # 波括弧を除去
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    self.add_file_to_list(file_path)

            self.log_message(f"{len(files)}個のファイルを追加しました")
        except Exception as e:
            self.log_message(f"ファイルドロップエラー: {e}")

    def on_drag_enter(self, event):
        """ドラッグエンター時の視覚的フィードバック"""
        self.file_tree.configure(style="DragOver.Treeview")

    def on_drag_leave(self, event):
        """ドラッグリーブ時の視覚的フィードバック"""
        self.file_tree.configure(style="Treeview")

    def paste_files_from_clipboard(self, event):
        """クリップボードからファイルを貼り付け（簡易版）"""
        try:
            # クリップボードからテキストを取得
            clipboard_text = self.root.clipboard_get()

            # 改行で分割してファイルパスとして処理
            lines = clipboard_text.strip().split('\n')
            added_count = 0

            for line in lines:
                line = line.strip()
                if line and os.path.exists(line) and os.path.isfile(line):
                    if self.add_file_to_list(line):
                        added_count += 1

            if added_count > 0:
                self.log_message(f"クリップボードから{added_count}個のファイルを追加しました")
            else:
                # ファイルパス入力ダイアログを表示
                self.show_file_path_input_dialog()

        except tk.TclError:
            # クリップボードが空の場合
            self.show_file_path_input_dialog()
        except Exception as e:
            self.log_message(f"クリップボードエラー: {e}")
            self.show_file_path_input_dialog()

    def show_file_path_input_dialog(self):
        """ファイルパス直接入力ダイアログ"""
        from tkinter import simpledialog

        dialog_text = """ファイルパスを直接入力してください。
複数のファイルは改行で区切ってください。

例:
C:\\Videos\\movie1.mp4
C:\\Videos\\movie2.avi"""

        file_paths = simpledialog.askstring(
            "ファイルパス入力",
            dialog_text,
            initialvalue=""
        )

        if file_paths:
            lines = file_paths.strip().split('\n')
            added_count = 0

            for line in lines:
                line = line.strip()
                if line and os.path.exists(line) and os.path.isfile(line):
                    if self.add_file_to_list(line):
                        added_count += 1

            if added_count > 0:
                self.log_message(f"{added_count}個のファイルを追加しました")
            else:
                messagebox.showwarning("警告", "有効なファイルパスが見つかりませんでした。")

    def setup_file_drop_area(self):
        """ファイルドロップエリアの基本設定"""
        # ファイルリストエリアにコンテキストメニューを追加
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="ファイルを追加", command=self.add_files)
        self.context_menu.add_command(label="フォルダから追加", command=self.add_files_from_folder)
        self.context_menu.add_command(label="ファイルパスを直接入力", command=self.show_file_path_input_dialog)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="選択ファイルを削除", command=self.remove_selected_files)
        self.context_menu.add_command(label="すべてクリア", command=self.clear_all_files)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="クリップボードから貼り付け (Ctrl+V)",
                                    command=lambda: self.paste_files_from_clipboard(None))

        self.file_tree.bind("<Button-3>", self.show_context_menu)  # 右クリック

    def show_context_menu(self, event):
        """コンテキストメニューを表示"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def on_tree_click(self, event):
        """ツリービューのクリックイベント"""
        # 選択されたアイテムの情報を取得
        item = self.file_tree.selection()
        if item:
            # 選択されたファイルの詳細をログに表示
            filepath = self.file_tree.item(item[0])["values"][3]  # パスは4番目のカラム
            self.log_message(f"選択: {os.path.basename(filepath)}")

    def on_tree_double_click(self, event):
        """ツリービューのダブルクリックイベント"""
        item = self.file_tree.selection()
        if item:
            filepath = self.file_tree.item(item[0])["values"][3]  # パスは4番目のカラム
            # ファイルの詳細情報を表示
            self.show_file_info(filepath)

    def show_file_info(self, filepath):
        """ファイル情報を表示"""
        try:
            if not os.path.exists(filepath):
                messagebox.showerror("エラー", "ファイルが見つかりません。")
                return

            stat = os.stat(filepath)
            size_mb = stat.st_size / (1024 * 1024)
            from datetime import datetime
            mod_time = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")

            info = f"ファイル: {os.path.basename(filepath)}\n"
            info += f"パス: {filepath}\n"
            info += f"サイズ: {size_mb:.2f} MB\n"
            info += f"更新日時: {mod_time}"

            messagebox.showinfo("ファイル情報", info)
        except Exception as e:
            messagebox.showerror("エラー", f"ファイル情報の取得に失敗しました: {e}")

    def browse_ffmpeg(self):
        """FFmpeg実行ファイルの参照"""
        filetypes = [("実行ファイル", "*.exe"), ("すべてのファイル", "*.*")]
        if sys.platform != "win32":
            filetypes = [("すべてのファイル", "*.*")]

        filename = filedialog.askopenfilename(
            title="FFmpeg実行ファイルを選択",
            filetypes=filetypes
        )
        if filename:
            self.ffmpeg_path_var.set(filename)
            # FFmpegが設定されたら自動的にFFprobeも検出を試行
            self.auto_detect_ffprobe()

    def browse_ffprobe(self):
        """FFprobe実行ファイルの参照"""
        filetypes = [("実行ファイル", "*.exe"), ("すべてのファイル", "*.*")]
        if sys.platform != "win32":
            filetypes = [("すべてのファイル", "*.*")]

        filename = filedialog.askopenfilename(
            title="FFprobe実行ファイルを選択",
            filetypes=filetypes
        )
        if filename:
            self.ffprobe_path_var.set(filename)

    def auto_detect_ffprobe(self):
        """FFprobeの自動検出"""
        ffmpeg_path = self.ffmpeg_path_var.get().strip()
        if not ffmpeg_path:
            messagebox.showwarning("警告", "先にFFmpegパスを設定してください。")
            return

        # FFmpegと同じディレクトリでFFprobeを検索
        ffmpeg_dir = os.path.dirname(ffmpeg_path)
        ffmpeg_name = os.path.basename(ffmpeg_path)

        # 可能なFFprobe名のパターン
        if sys.platform == "win32":
            ffprobe_names = ["ffprobe.exe"]
            if ffmpeg_name.lower() == "ffmpeg.exe":
                ffprobe_names.insert(0, "ffprobe.exe")
        else:
            ffprobe_names = ["ffprobe"]
            if ffmpeg_name == "ffmpeg":
                ffprobe_names.insert(0, "ffprobe")

        # FFprobeファイルを検索
        for ffprobe_name in ffprobe_names:
            ffprobe_path = os.path.join(ffmpeg_dir, ffprobe_name)
            if os.path.exists(ffprobe_path):
                # FFprobeの動作確認
                try:
                    result = subprocess.run([ffprobe_path, "-version"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.ffprobe_path_var.set(ffprobe_path)
                        self.log_message(f"FFprobeを自動検出しました: {ffprobe_path}")
                        return
                except Exception:
                    continue

        # 見つからない場合
        messagebox.showinfo("情報",
                           f"FFprobeが見つかりませんでした。\n"
                           f"検索場所: {ffmpeg_dir}\n"
                           f"手動で設定してください。")

    def browse_output_folder(self):
        """出力フォルダの参照"""
        folder = filedialog.askdirectory(title="出力フォルダを選択")
        if folder:
            self.custom_output_var.set(folder)

    def add_files(self):
        """ファイル追加ダイアログ"""
        filetypes = [
            ("動画ファイル", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v"),
            ("すべてのファイル", "*.*")
        ]

        filenames = filedialog.askopenfilenames(
            title="処理対象ファイルを選択",
            filetypes=filetypes
        )

        added_count = 0
        for filename in filenames:
            if self.add_file_to_list(filename):
                added_count += 1

        if added_count > 0:
            self.log_message(f"{added_count}個のファイルを追加しました")

    def add_files_from_folder(self):
        """フォルダから動画ファイルを一括追加"""
        folder = filedialog.askdirectory(title="動画ファイルを含むフォルダを選択")
        if not folder:
            return

        # 対応する動画ファイル拡張子
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v',
                           '.MP4', '.AVI', '.MOV', '.MKV', '.WMV', '.FLV', '.WEBM', '.M4V'}

        added_count = 0
        try:
            # フォルダ内の動画ファイルを検索
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if any(file.endswith(ext) for ext in video_extensions):
                        filepath = os.path.join(root, file)
                        if self.add_file_to_list(filepath):
                            added_count += 1

            if added_count > 0:
                self.log_message(f"フォルダから{added_count}個の動画ファイルを追加しました")
            else:
                messagebox.showinfo("情報", "指定されたフォルダに動画ファイルが見つかりませんでした。")

        except Exception as e:
            messagebox.showerror("エラー", f"フォルダの読み込み中にエラーが発生しました: {e}")

    def add_file_to_list(self, filepath):
        """ファイルをリストに追加"""
        # ファイルの存在確認
        if not os.path.exists(filepath) or not os.path.isfile(filepath):
            return False

        # 重複チェック
        for item in self.file_tree.get_children():
            if self.file_tree.item(item)["values"][1] == filepath:
                return False  # 既に追加済み

        # ファイル情報を追加（進捗バー・時間付き）
        progress_text = self.create_progress_bar_text(0)
        self.file_tree.insert("", "end", values=("待機中", progress_text, "00:00", filepath))

        # 内部リストにも追加
        self.file_list.append({
            "path": filepath,
            "status": "waiting",
            "output_path": "",
            "error_message": "",
            "progress": 0,  # 進捗率
            "start_time": None,
            "end_time": None
        })

        return True

    def remove_selected_files(self):
        """選択されたファイルを削除"""
        selected_items = self.file_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "削除するファイルを選択してください。")
            return

        # 逆順で削除（インデックスの変更を避けるため）
        for item in reversed(selected_items):
            filepath = self.file_tree.item(item)["values"][3]  # パスは4番目のカラム
            self.file_tree.delete(item)

            # 内部リストからも削除
            self.file_list = [f for f in self.file_list if f["path"] != filepath]

    def clear_all_files(self):
        """すべてのファイルをクリア"""
        if self.file_list and messagebox.askokcancel("確認", "すべてのファイルをクリアしますか？"):
            self.file_tree.delete(*self.file_tree.get_children())
            self.file_list.clear()

    def log_message(self, message):
        """ログメッセージを追加"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def create_progress_bar_text(self, progress_percent):
        """進捗バーのテキスト表現を作成"""
        bar_length = 20
        filled_length = int(bar_length * progress_percent / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        return f"{bar} {progress_percent:3.0f}%"

    def format_elapsed_time(self, file_info):
        """開始時刻からの経過時間を mm:ss / hh:mm:ss で返す"""
        try:
            if not file_info:
                return ""
            start = file_info.get("start_time")
            end = file_info.get("end_time")
            if not start:
                return "00:00"
            elapsed = (end or time.time()) - start
            h = int(elapsed // 3600)
            m = int((elapsed % 3600) // 60)
            s = int(elapsed % 60)
            return f"{h:02}:{m:02}:{s:02}" if h else f"{m:02}:{s:02}"
        except Exception:
            return "00:00"

    def update_file_status(self, filepath, status, error_message="", progress=None):
        """ファイルのステータスと進捗を更新"""
        # 内部リスト更新
        for file_info in self.file_list:
            if file_info["path"] == filepath:
                file_info["status"] = status
                file_info["error_message"] = error_message
                if progress is not None:
                    file_info["progress"] = progress
                break

        # GUI更新
        for item in self.file_tree.get_children():
            if self.file_tree.item(item)["values"][3] == filepath:  # パスは4番目のカラム
                status_text = {
                    "waiting": "待機中",
                    "processing": "処理中",
                    "completed": "完了",
                    "error": "エラー"
                }.get(status, status)

                # 進捗率の取得
                current_progress = progress if progress is not None else file_info.get("progress", 0)
                if status == "completed":
                    current_progress = 100
                    file_info["end_time"] = file_info.get("end_time") or time.time()
                elif status == "error":
                    current_progress = 0
                    file_info["end_time"] = file_info.get("end_time") or time.time()

                # 経過時間表示
                elapsed_str = self.format_elapsed_time(file_info)
                progress_text = self.create_progress_bar_text(current_progress)
                self.file_tree.item(item, values=(status_text, progress_text, elapsed_str, filepath))
                break

    def update_file_progress(self, filepath, progress_percent):
        """ファイルの進捗のみを更新"""
        # 内部リスト更新
        for file_info in self.file_list:
            if file_info["path"] == filepath:
                file_info["progress"] = progress_percent
                break

        # GUI更新
        for item in self.file_tree.get_children():
            if self.file_tree.item(item)["values"][3] == filepath:
                current_values = self.file_tree.item(item)["values"]
                progress_text = self.create_progress_bar_text(progress_percent)

                # 経過時間の更新
                file_info = next((f for f in self.file_list if f["path"] == filepath), None)
                elapsed_str = self.format_elapsed_time(file_info) if file_info else ""

                self.file_tree.item(item, values=(current_values[0], progress_text, elapsed_str, current_values[3]))
                break

    def load_settings(self):
        """設定ファイルから設定を読み込み"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
        except Exception as e:
            print(f"設定読み込みエラー: {e}")

    def save_settings(self):
        """設定をファイルに保存"""
        try:
            # 現在のGUI状態を設定に反映
            self.settings["ffmpeg_path"] = self.ffmpeg_path_var.get()
            self.settings["ffprobe_path"] = self.ffprobe_path_var.get()
            self.settings["output_mode"] = self.output_mode_var.get()
            self.settings["custom_output_path"] = self.custom_output_var.get()
            self.settings["create_subfolder"] = self.subfolder_var.get()
            self.settings["extraction_mode"] = self.extraction_mode_var.get()
            self.settings["audio_bitrate"] = self.bitrate_var.get()
            self.settings["parallel_count"] = self.parallel_var.get()

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"設定保存エラー: {e}")

    def update_progress(self):
        """進捗状況の更新"""
        try:
            while not self.progress_queue.empty():
                message_type, data = self.progress_queue.get_nowait()

                if message_type == "progress":
                    completed, total = data
                    if total > 0:
                        progress = (completed / total) * 100
                        self.progress_var.set(progress)
                        self.progress_label.config(text=f"{completed}/{total}")

                elif message_type == "log":
                    self.log_message(data)

                elif message_type == "status":
                    if len(data) == 3:
                        filepath, status, error_msg = data
                        self.update_file_status(filepath, status, error_msg)
                    elif len(data) == 4:
                        filepath, status, error_msg, progress = data
                        self.update_file_status(filepath, status, error_msg, progress)

                elif message_type == "file_progress":
                    filepath, progress_percent = data
                    self.update_file_progress(filepath, progress_percent)

                elif message_type == "finished":
                    self.processing = False
                    self.start_button.config(state=tk.NORMAL)
                    self.stop_button.config(state=tk.DISABLED)
                    self.log_message("処理が完了しました。")

        except queue.Empty:
            pass

        # 次の更新をスケジュール
        self.root.after(100, self.update_progress)

    def start_processing(self):
        """処理開始"""
        # 入力検証
        if not self.validate_settings():
            return

        if not self.file_list:
            messagebox.showwarning("警告", "処理対象ファイルがありません。")
            return

        # UI状態更新
        self.processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        # 進捗リセット
        self.progress_var.set(0)
        self.progress_label.config(text="0/0")
        self.log_text.delete(1.0, tk.END)

        # すべてのファイルステータスを待機中にリセット
        for file_info in self.file_list:
            file_info["status"] = "waiting"
            file_info["progress"] = 0
            self.update_file_status(file_info["path"], "waiting", "", 0)

        self.log_message("処理を開始します...")

        # バックグラウンドで処理実行
        threading.Thread(target=self.process_files, daemon=True).start()

    def stop_processing(self):
        """処理停止"""
        if self.processing:
            self.processing = False
            self.log_message("処理を中止しています...")

            # エグゼキューターを強制終了
            if self.executor:
                try:
                    self.executor.shutdown(wait=False)
                    # 少し待ってから強制終了
                    import threading
                    def force_shutdown():
                        import time
                        time.sleep(3)
                        if self.executor:
                            try:
                                # 実行中のタスクを強制終了
                                for future in self.executor._threads:
                                    if hasattr(future, '_state') and future._state == 'RUNNING':
                                        future.cancel()
                            except:
                                pass

                    threading.Thread(target=force_shutdown, daemon=True).start()
                except Exception as e:
                    self.log_message(f"エグゼキューター停止エラー: {e}")

            # UI状態を即座に更新
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.log_message("処理を中止しました。")

    def validate_settings(self):
        """設定の検証"""
        # FFmpegパスの検証
        ffmpeg_path = self.ffmpeg_path_var.get().strip()
        if not ffmpeg_path:
            messagebox.showerror("エラー", "FFmpegのパスを指定してください。")
            return False

        if not os.path.exists(ffmpeg_path):
            messagebox.showerror("エラー", "指定されたFFmpegファイルが見つかりません。")
            return False

        # FFmpegの実行可能性をテスト
        try:
            result = subprocess.run([ffmpeg_path, "-version"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                messagebox.showerror("エラー", "FFmpegの実行に失敗しました。")
                return False
        except Exception as e:
            messagebox.showerror("エラー", f"FFmpegのテストに失敗しました: {e}")
            return False

        # FFprobeパスの検証（オプション）
        ffprobe_path = self.ffprobe_path_var.get().strip()
        if ffprobe_path:
            if not os.path.exists(ffprobe_path):
                if messagebox.askyesno("確認",
                                     "指定されたFFprobeファイルが見つかりません。\n"
                                     "FFprobeなしで続行しますか？\n"
                                     "（進捗表示の精度が低下します）"):
                    self.ffprobe_path_var.set("")  # FFprobeパスをクリア
                else:
                    return False
            else:
                # FFprobeの実行可能性をテスト
                try:
                    result = subprocess.run([ffprobe_path, "-version"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode != 0:
                        messagebox.showwarning("警告", "FFprobeの実行に失敗しました。進捗表示の精度が低下します。")
                        self.ffprobe_path_var.set("")
                except Exception as e:
                    messagebox.showwarning("警告", f"FFprobeのテストに失敗しました: {e}\n進捗表示の精度が低下します。")
                    self.ffprobe_path_var.set("")

        # 出力先の検証
        if self.output_mode_var.get() == "custom_folder":
            output_path = self.custom_output_var.get().strip()
            if not output_path:
                messagebox.showerror("エラー", "出力フォルダを指定してください。")
                return False

            if not os.path.exists(output_path):
                try:
                    os.makedirs(output_path, exist_ok=True)
                except Exception as e:
                    messagebox.showerror("エラー", f"出力フォルダの作成に失敗しました: {e}")
                    return False

        return True

    def process_files(self):
        """ファイル処理のメイン関数（バックグラウンド実行）"""
        try:
            total_files = len(self.file_list)
            completed_files = 0

            # 初期進捗を送信
            self.progress_queue.put(("progress", (0, total_files)))

            # 並列処理の設定
            max_workers = self.parallel_var.get()

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                self.executor = executor

                # 処理対象ファイルのフューチャーを作成
                future_to_file = {}
                for file_info in self.file_list:
                    if not self.processing:
                        break

                    future = executor.submit(self.process_single_file, file_info)
                    future_to_file[future] = file_info

                # 完了したタスクを処理
                for future in concurrent.futures.as_completed(future_to_file):
                    if not self.processing:
                        break

                    file_info = future_to_file[future]
                    try:
                        success, error_msg = future.result()
                        completed_files += 1  # 成功・失敗に関わらず完了数をカウント

                        if success:
                            self.progress_queue.put(("status", (file_info["path"], "completed", "")))
                            self.progress_queue.put(("log", f"完了: {os.path.basename(file_info['path'])}"))
                        else:
                            self.progress_queue.put(("status", (file_info["path"], "error", error_msg)))
                            self.progress_queue.put(("log", f"エラー: {os.path.basename(file_info['path'])} - {error_msg}"))

                    except Exception as e:
                        completed_files += 1  # 例外でも完了数をカウント
                        self.progress_queue.put(("status", (file_info["path"], "error", str(e))))
                        self.progress_queue.put(("log", f"例外エラー: {os.path.basename(file_info['path'])} - {e}"))

                    # 進捗更新（完了数ベースで更新）
                    self.progress_queue.put(("progress", (completed_files, total_files)))

        except Exception as e:
            self.progress_queue.put(("log", f"処理中にエラーが発生しました: {e}"))

        finally:
            self.executor = None
            self.progress_queue.put(("finished", None))

    def process_single_file(self, file_info):
        """単一ファイルの処理"""
        try:
            input_path = file_info["path"]

            # 処理中ステータスに更新
            # 開始時刻を記録
            file_info["start_time"] = time.time()
            file_info["end_time"] = None
            self.progress_queue.put(("status", (input_path, "processing", "", 0)))
            self.progress_queue.put(("log", f"処理開始: {os.path.basename(input_path)}"))

            # 出力パスの決定
            output_path = self.determine_output_path(input_path)
            file_info["output_path"] = output_path

            # 出力ディレクトリの作成
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # FFmpegコマンドの生成
            command = self.build_ffmpeg_command(input_path, output_path)
            self.progress_queue.put(("log", f"実行コマンド: {' '.join(command)}"))

            # 初期進捗を設定
            self.progress_queue.put(("file_progress", (input_path, 5)))

            # FFmpeg実行（進捗監視付き）
            success, error_msg = self.run_ffmpeg_with_progress(command, input_path)

            # 進捗監視が失敗した場合のフォールバック
            if not success and "進捗監視" in error_msg:
                self.progress_queue.put(("log", "進捗監視なしで再実行します..."))
                success, error_msg = self.run_ffmpeg_simple(command, input_path)

            if success:
                self.progress_queue.put(("file_progress", (input_path, 100)))
                return True, ""
            else:
                self.progress_queue.put(("file_progress", (input_path, 0)))  # エラー時は0%に戻す
                return False, error_msg

        except subprocess.TimeoutExpired:
            return False, "処理がタイムアウトしました"
        except Exception as e:
            return False, str(e)

    def run_ffmpeg_with_progress(self, command, input_path):
        """FFmpegを進捗監視付きで実行"""
        import time
        import select
        import sys

        try:
            # まず動画の長さを取得
            duration = self.get_video_duration(input_path)
            self.progress_queue.put(("log", f"動画長さ: {duration:.2f}秒"))

            # シンプルなFFmpegコマンドを使用（-progressは問題を起こす可能性があるため削除）
            # FFmpegを実行（リアルタイム出力）
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            # 進捗監視（タイムアウト付き）
            stderr_lines = []
            start_time = time.time()
            max_timeout = max(duration * 2, 300)  # 動画長さの2倍、最低5分
            last_progress_time = start_time

            while True:
                # プロセス状態をチェック
                poll_result = process.poll()
                current_time = time.time()

                # タイムアウトチェック
                if current_time - start_time > max_timeout:
                    self.progress_queue.put(("log", "処理がタイムアウトしました。プロセスを終了します。"))
                    process.terminate()
                    time.sleep(2)
                    if process.poll() is None:
                        process.kill()
                    return False, "処理がタイムアウトしました"

                # プロセスが終了している場合
                if poll_result is not None:
                    # 残りの出力を読み取り
                    try:
                        remaining_stdout, remaining_stderr = process.communicate(timeout=5)
                        if remaining_stderr:
                            stderr_lines.append(remaining_stderr)
                    except subprocess.TimeoutExpired:
                        pass
                    break

                # 出力を非ブロッキングで読み取り
                try:
                    # Windowsでは select は使えないので、readline with timeout を使用
                    if sys.platform == "win32":
                        # 短時間で読み取り試行
                        stderr_line = ""
                        if process.stderr:
                            # 非ブロッキング読み取りの代替
                            import threading
                            import queue as thread_queue

                            def read_stderr():
                                try:
                                    return process.stderr.readline()
                                except:
                                    return ""

                            # 短時間で読み取り
                            stderr_line = read_stderr()
                    else:
                        # Unix系では select を使用
                        ready, _, _ = select.select([process.stderr], [], [], 0.1)
                        stderr_line = process.stderr.readline() if ready else ""

                    if stderr_line:
                        stderr_lines.append(stderr_line)
                        last_progress_time = current_time

                        # 進捗情報を抽出
                        time_match = self.extract_time_from_ffmpeg_output(stderr_line)
                        if time_match and duration > 0:
                            progress = min((time_match / duration) * 100, 99)
                            self.progress_queue.put(("file_progress", (input_path, progress)))

                    # 進捗が長時間更新されない場合の推定進捗
                    elif current_time - last_progress_time > 10:  # 10秒間更新がない場合
                        elapsed = current_time - start_time
                        estimated_progress = min((elapsed / max_timeout) * 80, 80)  # 80%まで
                        self.progress_queue.put(("file_progress", (input_path, estimated_progress)))
                        last_progress_time = current_time

                except Exception as e:
                    self.progress_queue.put(("log", f"出力読み取りエラー: {e}"))

                # 処理中断チェック
                if not self.processing:
                    self.progress_queue.put(("log", "処理が中断されました"))
                    process.terminate()
                    time.sleep(1)
                    if process.poll() is None:
                        process.kill()
                    return False, "処理が中断されました"

                # 短時間待機
                time.sleep(0.1)

            # 最終結果の確認
            return_code = process.returncode

            if return_code == 0:
                return True, ""
            else:
                error_output = ''.join(stderr_lines) if stderr_lines else "FFmpegの実行に失敗しました"
                return False, error_output

        except Exception as e:
            self.progress_queue.put(("log", f"進捗監視エラー: {e}"))
            # プロセスが残っている場合は終了
            try:
                if 'process' in locals() and process.poll() is None:
                    process.terminate()
                    time.sleep(1)
                    if process.poll() is None:
                        process.kill()
            except:
                pass
            return False, str(e)

    def parse_ffmpeg_progress_output(self, line, duration):
        """FFmpegの-progressオプションの出力を解析"""
        try:
            if "out_time_ms=" in line:
                # マイクロ秒単位の時間を取得
                time_ms = int(line.split("out_time_ms=")[1].strip())
                current_seconds = time_ms / 1000000.0  # マイクロ秒を秒に変換
                if duration > 0:
                    return (current_seconds / duration) * 100
            elif "out_time=" in line:
                # HH:MM:SS.mmm形式の時間を取得
                time_str = line.split("out_time=")[1].strip()
                current_seconds = self.parse_time_string(time_str)
                if current_seconds and duration > 0:
                    return (current_seconds / duration) * 100
            return None
        except Exception:
            return None

    def parse_time_string(self, time_str):
        """HH:MM:SS.mmm形式の時間文字列を秒に変換"""
        try:
            import re
            match = re.match(r"(\d{2}):(\d{2}):(\d{2})\.(\d{3})", time_str)
            if match:
                hours, minutes, seconds, milliseconds = map(int, match.groups())
                return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return None
        except Exception:
            return None

    def get_video_duration(self, input_path):
        """動画の長さを取得（秒）"""
        try:
            # 方法1: 設定されたFFprobeを使用（最も正確）
            ffprobe_path = self.ffprobe_path_var.get().strip()
            if ffprobe_path and os.path.exists(ffprobe_path):
                try:
                    ffprobe_command = [
                        ffprobe_path,
                        "-v", "quiet",
                        "-show_entries", "format=duration",
                        "-of", "csv=p=0",
                        input_path
                    ]

                    result = subprocess.run(ffprobe_command, capture_output=True, text=True, timeout=30)
                    if result.returncode == 0 and result.stdout.strip():
                        duration = float(result.stdout.strip())
                        if duration > 0:
                            self.progress_queue.put(("log", f"FFprobeで動画長さを取得: {duration:.2f}秒"))
                            return duration
                except Exception as e:
                    self.progress_queue.put(("log", f"FFprobe失敗: {e}"))

            # 方法2: FFmpegと同じディレクトリのFFprobeを自動検出
            ffmpeg_path = self.ffmpeg_path_var.get()
            if ffmpeg_path:
                auto_ffprobe_path = ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe").replace("ffmpeg", "ffprobe")
                if os.path.exists(auto_ffprobe_path):
                    try:
                        ffprobe_command = [
                            auto_ffprobe_path,
                            "-v", "quiet",
                            "-show_entries", "format=duration",
                            "-of", "csv=p=0",
                            input_path
                        ]

                        result = subprocess.run(ffprobe_command, capture_output=True, text=True, timeout=30)
                        if result.returncode == 0 and result.stdout.strip():
                            duration = float(result.stdout.strip())
                            if duration > 0:
                                self.progress_queue.put(("log", f"自動検出FFprobeで動画長さを取得: {duration:.2f}秒"))
                                return duration
                    except Exception as e:
                        self.progress_queue.put(("log", f"自動検出FFprobe失敗: {e}"))

            # 方法3: FFmpegの-iオプションで情報取得
            duration = self.estimate_duration_from_ffmpeg(input_path)
            if duration > 0:
                self.progress_queue.put(("log", f"FFmpegで動画長さを推定: {duration:.2f}秒"))
                return duration

            # 方法4: ファイルサイズベースの推定（最後の手段）
            duration = self.estimate_duration_from_filesize(input_path)
            self.progress_queue.put(("log", f"ファイルサイズから動画長さを推定: {duration:.2f}秒"))
            return duration

        except Exception as e:
            self.progress_queue.put(("log", f"動画長さ取得エラー: {e}"))
            return 300  # デフォルト5分

    def estimate_duration_from_filesize(self, input_path):
        """ファイルサイズから動画長さを推定（非常に大雑把）"""
        try:
            file_size = os.path.getsize(input_path)
            # 1MBあたり約10秒と仮定（非常に大雑把な推定）
            estimated_duration = (file_size / (1024 * 1024)) * 10
            return max(estimated_duration, 60)  # 最低60秒とする
        except Exception:
            return 300  # デフォルト5分

    def estimate_duration_from_ffmpeg(self, input_path):
        """FFmpegの出力から動画の長さを推定"""
        try:
            ffmpeg_path = self.ffmpeg_path_var.get()
            command = [ffmpeg_path, "-i", input_path]

            result = subprocess.run(command, capture_output=True, text=True, timeout=30)

            # "Duration: HH:MM:SS.ms" の形式を検索
            import re
            duration_match = re.search(r"Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})", result.stderr)
            if duration_match:
                hours, minutes, seconds, centiseconds = map(int, duration_match.groups())
                total_seconds = hours * 3600 + minutes * 60 + seconds + centiseconds / 100
                return total_seconds

            return 0
        except Exception:
            return 0

    def extract_time_from_ffmpeg_output(self, output):
        """FFmpegの出力から現在の処理時間を抽出"""
        try:
            import re

            # 複数のパターンを試行
            patterns = [
                r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})",  # time=HH:MM:SS.cc
                r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{3})",  # time=HH:MM:SS.mmm
                r"time=(\d{1,2}):(\d{2}):(\d{2})\.(\d{2})", # time=H:MM:SS.cc
                r"time=(\d{1,2}):(\d{2}):(\d{2})"          # time=H:MM:SS
            ]

            for pattern in patterns:
                time_match = re.search(pattern, output)
                if time_match:
                    groups = time_match.groups()
                    hours = int(groups[0])
                    minutes = int(groups[1])
                    seconds = int(groups[2])

                    # 小数部分の処理
                    if len(groups) > 3:
                        fraction = int(groups[3])
                        if len(groups[3]) == 2:  # centiseconds
                            fraction = fraction / 100
                        elif len(groups[3]) == 3:  # milliseconds
                            fraction = fraction / 1000
                    else:
                        fraction = 0

                    total_seconds = hours * 3600 + minutes * 60 + seconds + fraction
                    return total_seconds

            return None
        except Exception:
            return None

    def determine_output_path(self, input_path):
        """出力パスの決定"""
        input_dir = os.path.dirname(input_path)
        input_name = os.path.splitext(os.path.basename(input_path))[0]

        # 拡張子の決定
        extraction_mode = self.extraction_mode_var.get()
        if extraction_mode == "copy":
            # 元の音声形式を保持（一般的にはm4a）
            extension = ".m4a"
        elif extraction_mode == "mp3":
            extension = ".mp3"
        elif extraction_mode == "aac":
            extension = ".aac"
        else:
            extension = ".m4a"

        # 出力先の決定
        if self.output_mode_var.get() == "same_folder":
            output_dir = input_dir
        else:
            output_dir = self.custom_output_var.get()

            # サブフォルダ作成オプション
            if self.subfolder_var.get():
                output_dir = os.path.join(output_dir, input_name)

        return os.path.join(output_dir, input_name + extension)

    def build_ffmpeg_command(self, input_path, output_path):
        """FFmpegコマンドの構築"""
        ffmpeg_path = self.ffmpeg_path_var.get()
        extraction_mode = self.extraction_mode_var.get()

        command = [ffmpeg_path, "-i", input_path]

        if extraction_mode == "copy":
            # 無劣化コピー
            command.extend(["-vn", "-acodec", "copy"])
        elif extraction_mode == "mp3":
            # MP3変換
            bitrate = self.bitrate_var.get()
            command.extend(["-vn", "-acodec", "libmp3lame", "-ab", f"{bitrate}k"])
        elif extraction_mode == "aac":
            # AAC変換
            bitrate = self.bitrate_var.get()
            command.extend(["-vn", "-acodec", "aac", "-ab", f"{bitrate}k"])

        # 出力ファイル（上書き確認なし）
        command.extend(["-y", output_path])

        return command

    def run_ffmpeg_simple(self, command, input_path):
        """シンプルなFFmpeg実行（進捗監視なし、タイマーベース進捗）"""
        import time

        try:
            # 推定処理時間を計算
            duration = self.get_video_duration(input_path)
            estimated_time = max(duration * 0.2, 30)  # 動画長さの20%、最低30秒
            max_timeout = max(duration * 3, 600)  # 最大タイムアウト：動画長さの3倍、最低10分

            self.progress_queue.put(("log", f"推定処理時間: {estimated_time:.1f}秒, 最大タイムアウト: {max_timeout:.1f}秒"))

            # バックグラウンドでFFmpegを実行
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )

            # タイマーベースの進捗表示
            start_time = time.time()

            while True:
                # プロセス状態をチェック
                poll_result = process.poll()
                current_time = time.time()
                elapsed = current_time - start_time

                # プロセスが終了している場合
                if poll_result is not None:
                    break

                # タイムアウトチェック
                if elapsed > max_timeout:
                    self.progress_queue.put(("log", "処理がタイムアウトしました。プロセスを終了します。"))
                    process.terminate()
                    time.sleep(2)
                    if process.poll() is None:
                        process.kill()
                    return False, "処理がタイムアウトしました"

                # 処理中断チェック
                if not self.processing:
                    self.progress_queue.put(("log", "処理が中断されました"))
                    process.terminate()
                    time.sleep(1)
                    if process.poll() is None:
                        process.kill()
                    return False, "処理が中断されました"

                # 進捗更新
                progress = min((elapsed / estimated_time) * 90, 90)  # 90%まで
                self.progress_queue.put(("file_progress", (input_path, progress)))

                # 0.5秒間隔で更新
                time.sleep(0.5)

            # プロセス完了を待機（短時間のタイムアウト付き）
            try:
                stdout, stderr = process.communicate(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                stdout, stderr = process.communicate()

            if process.returncode == 0:
                return True, ""
            else:
                return False, stderr or "FFmpegの実行に失敗しました"

        except Exception as e:
            # プロセスが残っている場合は終了
            try:
                if 'process' in locals() and process.poll() is None:
                    process.terminate()
                    time.sleep(1)
                    if process.poll() is None:
                        process.kill()
            except:
                pass
            return False, str(e)

if __name__ == "__main__":
    app = FFmpegAudioExtractor()
    app.run()
