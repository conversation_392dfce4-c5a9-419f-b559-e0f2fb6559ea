# FFmpeg Audio Extractor GUI Tool

FFmpegを使用して動画ファイルからオーディオを抽出するGUIアプリケーションです。

## 機能

- **直感的なGUI**: Tkinterベースの使いやすいインターフェース
- **一括処理**: 複数の動画ファイルを同時に処理
- **並列処理**: CPUコア数に応じた高速処理
- **柔軟な出力設定**: 出力先フォルダとファイル名の詳細設定
- **複数の抽出オプション**: 無劣化コピー、MP3変換、AAC変換
- **進捗表示**: リアルタイムの処理状況とログ表示
- **設定保存**: アプリケーション設定の自動保存

## 必要な環境

- Python 3.7以上
- FFmpeg（別途インストールが必要）
- Windows 10/11（推奨）

## インストールと使用方法

### 1. FFmpegのインストール

1. [FFmpeg公式サイト](https://ffmpeg.org/download.html)からFFmpegをダウンロード
2. 適当なフォルダに展開（例: `C:\ffmpeg\`）
3. ffmpeg.exeのパスをメモしておく

### 2. アプリケーションの起動

```bash
python main.py
```

### 3. 基本的な使用手順

1. **FFmpegパスの設定**: 「参照...」ボタンでffmpeg.exeを選択
2. **抽出オプションの選択**: 無劣化コピー、MP3変換、AAC変換から選択
3. **出力先の設定**: 元フォルダまたは指定フォルダを選択
4. **ファイルの追加**: 「ファイルを追加」ボタンで動画ファイルを選択
5. **処理開始**: 「処理開始」ボタンで一括処理を実行

## 対応ファイル形式

### 入力（動画ファイル）
- MP4, AVI, MOV, MKV, WMV, FLV, WebM, M4V など

### 出力（音声ファイル）
- **無劣化コピー**: M4A（元の音声形式を保持）
- **MP3変換**: MP3（128/192/256/320 kbps）
- **AAC変換**: AAC（128/192/256/320 kbps）

## 設定項目

### FFmpegパス
- FFmpeg実行ファイル（ffmpeg.exe）のフルパスを指定

### 抽出オプション
- **無劣化コピー**: 音声を再エンコードせずにそのまま抽出
- **MP3に変換**: 指定ビットレートでMP3に変換
- **AACに変換**: 指定ビットレートでAACに変換

### 出力先設定
- **元のファイルと同じフォルダ**: 入力ファイルと同じ場所に出力
- **指定フォルダ**: ユーザーが指定したフォルダに出力
- **サブフォルダ作成**: ファイル名のサブフォルダを作成して保存

### 並列処理数
- 同時に処理するファイル数（1〜CPUコア数）

## トラブルシューティング

### FFmpegが見つからない
- FFmpegが正しくインストールされているか確認
- パスに日本語や特殊文字が含まれていないか確認

### 処理が失敗する
- 入力ファイルが破損していないか確認
- 出力先フォルダに書き込み権限があるか確認
- ディスク容量が十分にあるか確認

### 処理が遅い
- 並列処理数を調整（CPUコア数以下に設定）
- 無劣化コピーを使用（変換処理が不要な場合）

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。

## 注意事項

- このアプリケーションはFFmpegのフロントエンドです
- FFmpeg自体は別途インストールが必要です
- 著作権で保護されたコンテンツの処理には注意してください
