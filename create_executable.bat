@echo off
echo ========================================
echo FFmpeg Audio Extractor 実行ファイル作成
echo ========================================
echo.

cd /d "%~dp0"

REM 1. 必要なライブラリをインストール
echo 1. 必要なライブラリをインストール中...
pip install pyinstaller pillow

REM 2. アイコンファイルを作成
echo.
echo 2. アイコンファイルを作成中...
python create_icon.py

REM 3. 実行ファイルを作成
echo.
echo 3. 実行ファイルを作成中...
if exist icon.ico (
    pyinstaller --onefile --windowed --name "FFmpeg_Audio_Extractor" --icon=icon.ico main.py
) else (
    echo アイコンファイルが見つかりません。アイコンなしで作成します。
    pyinstaller --onefile --windowed --name "FFmpeg_Audio_Extractor" main.py
)

REM 4. 結果を表示
echo.
echo ========================================
echo 作成完了！
echo ========================================
echo.
if exist "dist\FFmpeg_Audio_Extractor.exe" (
    echo ✓ 実行ファイルが正常に作成されました
    echo   場所: dist\FFmpeg_Audio_Extractor.exe
    echo.
    echo このファイルをダブルクリックで実行できます。
    echo 他のPCでも動作します（Pythonのインストール不要）。
) else (
    echo ✗ 実行ファイルの作成に失敗しました
    echo   エラーログを確認してください。
)

echo.
echo distフォルダを開きますか？ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    if exist dist (
        explorer dist
    )
)

echo.
pause
