#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡単なアイコンファイルを作成するスクリプト
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """シンプルなアイコンを作成"""
    # 256x256のアイコンを作成
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 背景の円
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(70, 130, 180, 255), outline=(30, 90, 140, 255), width=8)
    
    # 音符のような形を描画
    center_x, center_y = size // 2, size // 2
    
    # 音符の棒
    draw.rectangle([center_x - 8, center_y - 60, center_x + 8, center_y + 40], 
                  fill=(255, 255, 255, 255))
    
    # 音符の玉
    draw.ellipse([center_x - 25, center_y + 20, center_x + 25, center_y + 70], 
                fill=(255, 255, 255, 255))
    
    # 音符の旗
    points = [(center_x + 8, center_y - 60), 
              (center_x + 50, center_y - 40),
              (center_x + 45, center_y - 20),
              (center_x + 8, center_y - 35)]
    draw.polygon(points, fill=(255, 255, 255, 255))
    
    # 複数のサイズでアイコンを保存
    sizes = [16, 32, 48, 64, 128, 256]
    images = []
    
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        images.append(resized)
    
    # ICOファイルとして保存
    images[0].save('icon.ico', format='ICO', sizes=[(s, s) for s in sizes])
    print("アイコンファイル 'icon.ico' を作成しました")

if __name__ == "__main__":
    try:
        create_icon()
    except ImportError:
        print("Pillowライブラリが必要です。以下のコマンドでインストールしてください:")
        print("pip install Pillow")
    except Exception as e:
        print(f"アイコン作成エラー: {e}")
        print("デフォルトのアイコンなしで実行ファイルを作成します。")
